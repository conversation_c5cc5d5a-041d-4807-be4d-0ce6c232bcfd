# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
# See https://pyinstaller.org/en/stable/usage.html#using-a-spec-file
# for the spec-file naming convention
*.spec

# Installer logs
pip-log.txt
pip-delete-this-file.txt

# Unit test / coverage reports
.pytest_cache/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pylint.d/

# Jupyter Notebook files
.ipynb_checkpoints

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json

# environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# FastAPI specific
# The database file itself if you are using SQLite instead of PostgreSQL.
# In this case, since we're using PostgreSQL, we don't need to worry about it.

# VS Code specific
.vscode/
*.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

*.md
!README.md